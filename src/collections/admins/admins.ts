import type { CollectionConfig } from 'payload'
import { anyone, isSelfOrSuperAdmin, isSuperAdminAccess } from '@/collections/access'
import { AdminCollectionsGroup, collections } from '@/constants'

const {
  admins: { slug: adminsSlug, roles }
} = collections

export const admins: CollectionConfig = {
  slug: adminsSlug,
  access: {
    admin: isSuperAdminAccess,
    create: anyone,
    delete: isSuperAdminAccess,
    read: isSelfOrSuperAdmin,
    update: isSelfOrSuperAdmin
  },
  admin: {
    defaultColumns: ['name', 'email'],
    useAsTitle: 'name',
    group: AdminCollectionsGroup.application
  },
  auth: true,
  fields: [
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true
    },
    {
      name: 'name',
      type: 'text',
      required: true
    },
    {
      name: 'roles',
      type: 'select',
      options: [
        { label: 'Editor', value: roles.editor },
        { label: 'Admin', value: roles.admin }
      ],
      hasMany: false,
      required: true
    }
  ],
  timestamps: true
}
